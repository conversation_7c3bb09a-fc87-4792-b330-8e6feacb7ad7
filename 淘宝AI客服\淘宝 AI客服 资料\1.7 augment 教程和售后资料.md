```markdown
# 智切(Augment登陆助手) -使用教程和售后问题解决

## 售后wx群

### 推荐加售后群的原因:
1.  保持联系, 沟通及时
2.  续费不迷路
3.  问题解决迅速
4.  群内大神多
5.  加售后群就对了

## 售后群

![群聊: Augment 售后-3群](https://i.imgur.com/3h2a3jD.png)

*该二维码7天内(7月21日前)有效,重新进入将更新*

---

插件虽然已经做了很多处理,但偶尔还是会提示风险
**注:** 每个人的环境可能不一样,如果切换号后,使用几次还一直频繁出现风险提示2-3次以上。
请联系我们处理排查。别一直切换号,用完不负责。

**注意:** IDEA jetbrains系列因规则改变,暂不支持。支持Vscode/Cursor

### 最新适配
☑️ v0.502.2

---

## 1. Augment 安装下载
(ps: 安装好 augment的话,不用看,直接下一步)

打开 (Cursor/VS code) 的扩展市场, 将 `augment.vscode-augment-x.x.x.vsix` 文件拖拽到插件侧边栏,即可完成安装

![augment.vscode-augment-0.502.1.vsix](https://i.imgur.com/gYl85mG.png)

![拖拽安装Augment插件](https://i.imgur.com/2sJ6b8V.png)

---

## 2. 智切(Augment登陆助手) 下载

![smartshift-manager versions](https://i.imgur.com/T0a7d9p.png)

1.  智切插件版本保持最新的,可避免风控
2.  关闭augment插件自动更新,避免插件更新导致风控
3.  如果在 Remote SSH 中使用,需要在SSH中也安装智切插件

---

## 2.2 智切(Augment登陆助手) 安装

打开扩展市场, 将`smartshift-manager-x.x.x.vsix` 文件拖拽到插件侧边栏, 即可完成安装

![拖拽安装智切插件](https://i.imgur.com/o7O34e5.png)

## 2.3 智切(Augment登陆助手) 使用方法
1.  点击底部状态栏上的智切图标

---

## AI 智切 - 使用流程

![AI智切使用流程](https://i.imgur.com/z0j3p3h.png)

![AI智切界面](https://i.imgur.com/g9f5S8J.png)

2.  输入激活码, 点击登录, 点击“账号获取”即可获取帐号自动登录跟过设备限制
    *换号的时候只保留一个窗口, 把多余的工作区关闭, 换完可以开多个*

---

## 后续切号流程:

只保留一个IDE, 退出之前的augment帐号, 点击切换新帐号, 新建个对话, 这样无限稳定
有做了一些处理, 可以使用之前的聊天记录, 但可能有风险, 看情况

## 3. 关闭augment插件自动更新, 避免插件更新导致风控

![关闭Augment插件自动更新](https://i.imgur.com/K3Z9k7q.png)

## 4. 切换augment插件其他版本

![切换Augment插件版本](https://i.imgur.com/b9U3rJm.png)

---

## 5. 常见问题:

### 1. 点检查后提示 Augment plugin not found?
没有安装augment插件

### 2. 智切插件点击登录后, augment插件没有正常登录?
点击【获取】

### 3. 提示id限制?
**MacOS-intel**
![augclean-macos](https://i.imgur.com/P4Q9I4k.png)

**Windows**
![augclean-win.exe](https://i.imgur.com/V9X2a0Y.png)

**命令行执行**```bash
#macos
./augclean-macos

#windows
./augclean-win.exe
```

---

### 4. 重置后会话记录不在了?
安装SpecStory插件, 所有会话记录会保存在项目目录下.specstory文件夹, 重置后在会话@.specstory文件夹下内容作为上下文。
提示词:【请根据上述文件内容作为上下文,继续编写代码】

![安装SpecStory插件](https://i.imgur.com/R3x3f7N.png)

### 5. 登陆失败-1
`Client network socket disconnected before secure T1.5 connection was established`
`Please sign in below.`

![登录失败-1](https://i.imgur.com/r6n7r8N.png)

这个是开了梯子代理的原因, 关掉梯子中的系统代理

### 6. 登录失败-2
`:connect ECONNREFUSED 127.00.1:443`

![登录失败-2](https://i.imgur.com/I6P6w2o.png)

用梯子 开下全局代理

---
### 7. Augment一直genertating超时
解决方法：卸载重装下 augment，然后重启 VS code/Cursor

### 7. 如何看代码改动的前后对比

![代码改动对比](https://i.imgur.com/t9E6r6A.png)

### 8. Augment 官方下载链接
https://www.augmentcode.com/registration?status=already-signed-up

### 9. 将 Augement 移到右栏, 像Cursor一样对话
https://developer.volcengine.com/articles/7497876495386607667

### 10. Augment保姆级教程
▶️ 保姆级教程! Augment Code企业级AI编程智能体! 200k token超长上下窗口让AI真正理解复杂项目!
https://www.bilibili.com/video/BV1LBg9zpEyj/?spm_id_from=333.337.search-card.all.click&vd_source=1c05d5ba65bafd7e024b607706cfacfd

### 11. Augment Remote Agent (云端功能) 入门教程, AI 编程进入多 Agent 时代!
https://www.bilibili.com/video/BV1Ze3jzWEP5/?spm_id_from=333.337.search-card.all.click&vd_source=1c05d5ba65bafd7e024b607706cfacfd
```