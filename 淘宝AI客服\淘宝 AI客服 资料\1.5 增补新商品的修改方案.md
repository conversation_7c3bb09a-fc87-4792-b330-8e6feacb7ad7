我现在增加了一个商品， cursor pro 版的登陆助手。
和之前的（cursor登陆助手）的相同点：
1. 也是和之前的商品一样，使用 登录助手来登录，使用激活码来激活登录助手，来使用cursor。
2. max 还是是用不了，pro版的账号也需要额外充值才能使用
不同点：
1. 里面的账号都是 pro账号，不是之前的 试用账号。
2. 能使用 claude 4等付费模型
3.cursor pro版登陆助手 别的 cursor登陆助手普通版能用的模型都能用，比 普通版多的就是 claude 4 sonnet 这个无限用，但是 pro版，max 和 opus还是用不了，最多能用几次就用不了了。

你分析如何修改下这个项目，能够增补这个新的商品。
1. 在用户在这个商品里面问的时候，能够正确回答这个 cursor登陆助手 pro版的真实情况。
使用 cursor pro 说明书里面的内容来回答
2. 当用户在之前的cursor登陆助手里面问的时候，还是保持之前的回复内容，之前的产品没有变化。
3. 当客户问到 claude 4能不能用的时候
3.1 如果在之前的cursor登录助手中问，还是之前的回复内容，说 4等付费模型不支持，但支持 claude 3.7等主流模型。然后 推荐用户到新的 cursor登陆助手pro版的闲鱼商品链接，说这个可以用 claude 4.
3.2 如果在新的 cursor登陆助手pro版的闲鱼商品里面问，会回答这个 cursor登录助手pro版的真实情况，会回答能用。



分析我上面的需求，你分析如何修改这个项目。
