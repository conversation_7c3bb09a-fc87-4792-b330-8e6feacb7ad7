
# Cursor登录助手操作说明

## 售后wx群
推荐加售后群的原因:
1.  保持联系, 沟通及时
2.  续费不迷路
3.  问题解决迅速
4.  群内大神多
5.  加售后群就对了

### 售后群
![cursor售后群-6](https://i.imgur.com/kYd7Q2t.png)
*群聊: cursor售后群-6*
*该二维码7天内(8月3日前)有效, 重新进入将更新*

---

### 🚀 新店：【Cursor直营店】
- **淘宝 (推荐·自动发货)**
- 搜“Cursor直营店”, 或复制链接到浏览器/淘宝App打开:
  [`https://item.taobao.com/item.htm?id=931668414172&spm=a213gs.v2success.0.0.334c4831wpQDIT`](https://item.taobao.com/item.htm?id=931668414172&spm=a213gs.v2success.0.0.334c4831wpQDIT)


激活码定价 (30天有效):
·   4.9元 (200 额度)
·   8.9元 (500 额度)
·  12.9元 (1000 额度)
·  29.9元 (无限额度/单设备)


---

目前登录助手610版本已经集成Augment, Augment中的Cloued4是满血的, 也不用经常切换账号, 推荐使用, Cursor的使用情况, 目前有点复杂, 搞不了的, 可以直接使用Augment

[**Augment教程, 点击即可跳转**](about:blank)

> ✨ **新增cursor使用教程, 店主高价买来赠送给各位。点击这里就行**

---

## 下载登录助手最新版本610版本
- **windows:** [点击即可跳转](about:blank)
- **Mac-m芯片:** [点击即可跳转](about:blank)
- **Mac-intel芯片:** [点击即可跳转](about:blank)
> **下载报病毒问题:** 找一下点击保留按钮或者换个浏览器

---

# Cursor登录助手的使用教程(超级简单) (长期售后)

### 1. 下载与安装
- 复制上边的链接, 粘贴到浏览器下载即可。
- 下载完成后, 解压文件。

### 2. 运行程序
> **注意:** 运行登录助手的时候, **不要科学上网**, cursor登录上之后, 剩下就随意了

- **Windows 系统:** 右键点击下载的`.exe`程序文件, 选择“以管理员身份运行”。
- **Mac 系统:** 直接双击下载的有图标的程序, 即可运行。
  - Mac系统看到无法打开Cursor登录助手: 查看错误对照13
  - Mac系统如果看到“应用程序”已损坏

### 3. 激活登录助手
- 在登录助手界面输入激活码。
- 点击“激活设备”按钮完成激活。
- 激活之后可以查看登录助手的会员状态。

### 4. 重启登录助手
> **(激活之后不能直接点击刷新cursor按钮, 需要重启登录助手)**

### 5. 登录 Cursor
- 先运行Cursor程序。
- 点击登录助手中的“刷新 Cursor”按钮。**(不要频繁点击按钮, 点一次就行, 点之前记得重启登录助手)**
- 会有一个弹窗, 提醒您保存cursor项目, 您保存好之后再点击继续。
- 程序将自动完成 Cursor 的登录过程。
- 直接去Cursor中, 开始对话即可。如果不能对话, 根据提示, 请看下面的错误对照。

---

## Cursor登录助手界面说明
![Cursor登录助手界面](https://i.imgur.com/39aJ3Bw.png)

### 区域1: 主界面按钮操作说明
1.  **激活设备按钮:** 输入激活码后点击此按钮, 设备将被激活, 会员状态会随之更新。
2.  **刷新Cursor按钮:** 当单个账号没有额度时, 点击此按钮可切换到其他账号。
3.  **刷新额度按钮:** 点击此按钮可手动刷新剩余额度, 解决额度更新不及时的问题。
4.  **自动刷新按钮:** 此按钮亮点在于其自动刷新功能。打开时为蓝色, 表示自动刷新已开启, 会每30秒自动刷新剩余额度; 关闭时为白色, 表示自动刷新已关闭。

### 区域2: 会员状态区域
激活后显示的信息:
1.  **会员等级:** 显示当前会员的等级信息。
2.  **激活时间:** 显示设备激活的具体时间。
3.  **到期时间:** 显示会员资格的到期时间。
4.  **剩余额度:** 显示当前剩余的额度。
5.  **当前Cursor登录的邮箱:** 显示当前登录Cursor所使用的邮箱地址。

### 区域3: 设备激活码区域
显示当前设备使用的激活码:
- **显示激活码:** 在界面上显示当前设备所使用的激活码。
- **可复制按钮:** 显示一个可复制的按钮, 用户可以通过点击该按钮来复制激活码。

### 区域4: 当前账号额度监控区域
展示区域:
- **剩余额度次数:** 展示当前激活码的剩余额度次数。
- **进度条情况:** 通过进度条直观显示额度的使用进度。
**刷新额度按钮:**
- **功能:** 点击按钮, 会自动刷新当前所剩额度, 以确保显示的额度信息是最新的。

---

## 常见问题对照
> **【以下问题中, 95%的情况是常规问题, 但是网络或模型等问题, 通常与登录助手无关。】**

### 万能解决方案
问题多的电脑, 往往是本地的cursor环境不纯净。
**解决方案:**
提前保存好自己的项目, 并且卸载会将cursor相关的所有文件全部删除 (包括带cursor字样的文件夹)
1. 用教程里面的工具 (问题17) 卸载cursor。
2. 官网下载cursor, 没有版本限制。

### 17. 各系统卸载cursor及清理残留文件的软件推荐
- **Windows:** Geek
  - `geek.*********.exe` (6.69MB)
- **Mac:** [点击这里](about:blank)

---

## 0. 问题提示信息对照
> **代理必须是国外的哈, 香港是国内。**
> **注意注意, 这个不是账号的问题, pro账号, 和自己充值的账号也会有问题, 按照下面的方案没问题, 仔细按照教程走。**

- **问题提示一:** `Model not availableThis model provider doesn't serve yourregion.`
- **问题提示二:** `Set up usage billing for premium models`
- **问题提示三:** `Please contact an administrator in your organization to enable usagebased pricing`

都能解决, 解决不了的静下心来, 按照教程一步一步操作, 实在解决不了的, 稍等cursor那边修复, cursor官方的问题。
下面是cursor 官网发布的。
![Cursor官网论坛截图](https://i.imgur.com/u5uHqFh.png)

### 解决方案1
- 直接看问题对照2(下图位置), 跟那个一样的步骤
- 如果点击突破之后点击刷新新建对话之后没好的
- 新建项目, 然后再切换回来旧项目
> **方案一不行的, 用教程里面卸载工具, 卸载一下, 重新安装cursor**
![问题对照截图](https://i.imgur.com/D4J8g8b.png)

### 解决方案2
> **需要代理, 没有代理的自己找找吧, 这个是官方那边限制中国地区了, 有代理好解决。**

- **步骤一:** 升级cursor到1.0以上版本 (卸载用教程里面的工具卸载, 然后重新安装)
- **步骤二:** 左上角文件 - 首选项 - 设置 - 搜索proxy
  操作: 在应用程序->代理服务器中, 填入你的代理地址
  (例如: `http://127.0.0.1:7890`)
- **步骤三:** 左上角文件 - 首选项 - 设置 - 搜索http - 禁用HTTP2.0, 强行走http1.1, 代理才能生效
- **步骤四:** 自己的代理软件里面开全局 (必须)
  **注意:** 代理开全局, 是自己的代理, 不是登录助手的, 也不是cursor 的, 是自己的, 不要去各大平台去问客服, 客服不会回复代理以及vpn相关的问题。

#### 方案二的图文教程

**步骤二:**
![步骤二图示1](https://i.imgur.com/b2aW1Fm.png)
![步骤二图示2](https://i.imgur.com/b6b5y3v.png)

**步骤三 (HTTP设置):**
![步骤三图示](https://i.imgur.com/p0T7z4t.png)

- **步骤三 (代理软件):** 自己的代理软件, 开全局。(这个我就没有图了, 各位的代理app也不一样。)
- **步骤四:** 重启cursor, 新建对话

### 解决方案3
路径: cursor设置 -> network -> http2换成 -> http1
![解决方案3图示](https://i.imgur.com/w1iF7hO.png)

### 解决方案4
代理软件里面开tun模型, 开了之后重启cursor客户端。

### 解决方案5
> **Cursor限制国内使用claude gpt 大模型: 我找到了解决方法, 亲测可用! - 这个不用开全局, 不影响其他程序。配置后新开 cursor 窗口问答**

---

## 更多问题与解决方案

### 1. Cursor显示limit怎么办?

#### 1.1 Free users can only use 4.1 or Auto as premium models
![登录助手界面](https://i.imgur.com/5Jz8S4H.png)
> - 尽量使用0.46.1版本, 那个版本相对来说, 每个账号的使用额度多一些。
> - **win、mac-m、mac-intel:**
>   1. 教程里面下载登录助手608版本,
>   2. 管理员运行
>   3. 点击突破
>   4. 然后点击刷新cursor刷新
>   5. 新建对话
>   6. 先用4.1对话一次
>   7. 然后再换回其他想用的模型, 就可以
> - **频繁出现这个问题的, 那就是缓存问题了, 缓存太多了, 用方案17里面的工具卸载一下cursor, 要卸载干净, 然后重新下载一下**

#### 1.2 Your request has been blocked as our system has detected suspicious activity from your account.
![请求被阻止](https://i.imgur.com/i8sS8lP.png)
> **解决方法:**
> 1. cursor换成0.46.1版本或者0.48版本
> 2. 检查Cursor Settings的Rules, 清空User Rules配置。
> 3. 如果本来就没有配置, 随便加点, 如: "Chinese plz"或者"Always respond in Chinese"
> 4. 先用 GPT 4.1 或者其他任何免费的模型先发一条消息后再使用付费模型, 即可绕过
>
> **Cursor0.46.1下载链接:**
> [`https://cn.cursorhistory.com/versions/0.46.1`](https://cn.cursorhistory.com/versions/0.46.1)

### 1.3 禁用更新
![禁用更新](https://i.imgur.com/3nL3x3c.png)

### 2. 显示锁机器码怎么办? 也就是Cursor的Too many报错
![Too many free trials](https://i.imgur.com/3vB4O7f.png)
> #### 各系统通用方法:
> - **第一步:** 用工具卸载cursor, 然后下载cursor旧版本
> - **第二步:** 下载完之后不要打开cursor, 直接执行错误对照二的命令 (命令分系统呢, 看好是win还是mac命令)
> - **第三步:** 执行成功之后, 再打开cursor, 就好了。

> #### Windows系统:
> - 下载登录助手的608版本
> - 1、空点激活设备按钮, 也就是不输入激活码, 点击激活设备按钮。
> - 2、然后点击刷新cursor按钮。
> - 3、新建对话。
>
> **win如果突破不管用的话执行下面的命令**
> **步骤:**
> 1. powershell管理员身份运行
> 2. 直接复制下面的命令执行就欧克
> 3. 执行完之后, 登录助手点击刷新cursor按钮, 新建对话就欧克了
> ```powershell
> irm 'https://aizaozao.com/accelerate.php/https://raw.githubusercontent.com/LeaveC/go-cursor-help/refs/heads/master/scripts/run/cursor_win_id_modifier.ps1' | iex
> ```

> #### Mac-M芯片
> 1. 设置里面给终端管理员权限(下面的图片)
> 2. 终端执行下面的命令
> 3. 执行完之后, 登录助手点击刷新cursor按钮, 新建对话就欧克了
> ```bash
> sudo curl -fsSL http://************:8080/toomany-m -o /tmp/toomany-m && sudo chmod +x /tmp/toomany-m && sudo /tmp/toomany-m
> ```

> #### Mac-Intel命令
> ```bash
> sudo curl -fsSL http://************:8080/toomany-intel -o /tmp/toomany-intel && sudo chmod +x /tmp/toomany-intel && sudo /tmp/toomany-intel
> ```
![Mac终端权限设置](https://i.imgur.com/a4u8N2b.png)

> #### 下面是老命令, 想尝试的可以尝试一下。
> **Mac**
> 如果出现“limit”提示, 或者"Too many"提示, 点击“刷新 Cursor”仍无法使用:
> 请根据下面的操作进行
> 1. 打开终端。(根目录即可)
> 2. 将以下命令完整复制(点击复制按钮即可)
> 3. 执行完了之后, 重新执行Cursor登录助手, 点击刷刷新cursor按钮, 然后cursor新建对话即可。
>
> **先试终端命令, 100%成功率, 如果成功不了的话, 终端执行命令截图给客服。**
> ```bash
> curl -fsSL http://************:8080/go.sh | sudo bash
> ```

### 3. Cursor历史版本下载?
- [Cursor历史版本下载链接, 点击即可跳转](about:blank) (这个链接需要科学上网才能下载)
- **下面的是不需要科学上网就能下载的**
  - [cursor0.46版本-win(点击即可跳转)](about:blank)
  - [cursor0.46.1版本-mac(点击即可跳转)](about:blank)
  - [cursor0.46.1版本-intel(点击即可跳转)](about:blank)
- **更换版本-想保存历史对话 安装下面插件:**
  - **Cursor对话保存-插件**
  - [`https://cursordocs.com/tutorial/02-cursor-chat-history-export`](https://cursordocs.com/tutorial/02-cursor-chat-history-export)

### 4. 提示"Agent 和 Edit 依赖于自定义模型..."
![Pro Required](https://i.imgur.com/kS5z1Jd.png)
**答:** 查看下方的可用模型, 如果确定使用的是可用模型, 请打开cursor模型设置中下方把添加和打开所有API开关全部关闭

### 5. tab不可使用【自动补全无法使用】
**答:** 在教程中有让您登录您自己的cursor账号, 您那边尽量使用还剩有一点额度的账号登录, 不然登录助手无法使用到账号池里的账号【账号池里的账号全部都含有tab使用】
如果你没有更多的cursor账号了, [请点击这里获取](about:blank), 账号仅供内部使用【此表格内的账号为友情提供, 无任何售后及保障和解答, 请自行尝试】
下图是tab功能在cursor上的开关
![Tab补全开关](https://i.imgur.com/f9G7R6h.png)

### 6. Claude3.7、3.5模型不可使用的问题
- **关于模型使用情况**
  - **3.7模型:** 目前使用不稳定, 可能因刚发布且价格较贵, 官方做了部分限制, 导致部分人暂时无法使用。
  - **备用模型:** 3.5sonnet模型: 官方做了限制。
- **网络情况及应对措施**
  - **网络良好时:** 使用上述可使用的模型, 基本能做到快速回复且无降智。
  - 1、科学上网
  - 2、更换节点 (多换)
  - 3、新建对话
  - 先走这三步, 看看能不能绕过官方的高峰期限制。不行的话, 就是更换模型了。
> **目前国内就算是直充的pro也会有这个问题, 避免不了。只能通过换节点之后新建对话的方法解决。**
> 等候低峰期, 一般高峰期只是几个小时。
- **特别说明**
  此问题与登录助手无关, 主要是cursor的限制问题, 更换节点会绕过限制, 具体看各位的操作手法问题。

### 7. cursor更新了, 为什么composer不见了?
**答:** 请多查看下官方[更新日志](about:blank), 0.46版本把所有AI功能都集成到了chat里面【composer更改为了edit和agent】, 更多功能变化及使用方法请搜索各大视频平台。
- **1. 版本更新导致界面整合**
  Cursor 0.46 版本重新设计了 UI, 将原有的 Chat (问答模式) 和 Composer (代码生成/编辑模式) 合并为统一的交互界面。新版本通过以下模式替代原有功能:
  - **Agent 模式:** 对应原 Composer 的 Agent 模式 (需指定具体任务指令的交互);
  - **Ask 模式:** 对应原 Chat 的基础问答功能;
  - **Edit 模式:** 对应原 Composer 的普通代码生成和编辑功能。
- **2. 操作逻辑变化**
  新版本试图简化用户操作流程, 但可能让习惯旧版界面的用户感到不适应, 尤其是直接调用 Composer 的场景。

### 8. Your Free Trial Has Ended弹窗
> **直接关闭就行, 不影响**
- 如果不行的话, 再试下面的步骤
- **Cursor设置里面退出当前账号, 登录助手点击刷新Cursor按钮**
![Free Trial Ended](https://i.imgur.com/a9c9v6i.png)

### 9. 需要开梯子使用吗? 降智解决了吗? cursor突然自动删除了咋办?
**答:**
1. 登录助手使用过程中, 不需要科学上网。登录上之后, 都行
2. 降智问题已经解决
3. 突然自动删除是禁用更新失效了无法下载新版本, 到官网下载最新版本即可

### 10. 提示：“我们正在连接Anthropic时遇到问题。”
![Unable to reach anthropic](https://i.imgur.com/5O8wO4j.png)
- 目前可能没办法连接到 cursor 服务器, 或许是因为你的节点或者网络环境出了点问题。
- 你可以试着切换节点, 或者换一个网络环境再试试。
- 要是还是不行, 那可能是 cursor 服务器内的 anthropic 节点压力过大。
- 那你就先等等, 稍后再尝试吧。

### 11. 提示:"请检查您的互联网连接或VPN"
![Connection failed](https://i.imgur.com/B7c9l5L.png)
> #### 方法一:
> **答:** 如提示所述, 您的网络波动问题。
> 请切换一下国内网络, 手机热点。
> 如果还是不行, 就只能先用以下的方法了。
> 1. 科学上网
> 2. 更换节点
> 3. 重启cursor新建对话
> **如果还是报错, 请多切换几个节点试试【cursor服务器在海外, Cursor登录助手不做转发】**
> 没有科学上网的话, 就得等等了, 目前网络波动造成的。
> 可以尝试的方法: 1、重启电脑 2、稍等一个小时左右, 具体看电脑, 有些电脑需要等两个小时。
> 可以尝试以下方法, 但是需要自己操作, 客服那边就不涉及这些了。
> #### 方法二:
> 1. 打开Cursor, 选择文件 -> 首选项 -> 设置
> 2. 搜索框中输入: `disable`
> 3. 找到http2, 勾选就行
> 4. 然后重启cursor, 新建对话。
![禁用HTTP2](https://i.imgur.com/D1j4u5r.png)

### 12. JavaScript error报错。
> **这个报错的原因是重复点击激活按钮导致的, 已经激活的话, MAc就不用再次点击激活了。**
> 1. 点击, 好, 然后重新打开cursor
> 2. 不行的话, 就重新下载cursor。换成0.46-最新版本。
![JavaScript Error](https://i.imgur.com/j1Q2z3b.png)

### 13. mac显示无法打开安全助手
1. 点击-好或者完成
2. 打开设置-安全与隐私
3. 下拉界面, 选择仍然打开。
重新打开cursor登陆助手即可
![Mac安全提示](https://i.imgur.com/s6n5F4s.png)

### 14. Mac系统看懂“应该程序”已损坏。
- Mac系统如果看到“应用程序”已损坏, 无法打开的解决方法:
  [`https://sysin.org/blog/macos-if-crashes-when-opening/`](https://sysin.org/blog/macos-if-crashes-when-opening/)

### 15. Agent功能
![Agent功能说明](https://i.imgur.com/1gH2t5I.png)

### 16. Windows系统报毒
关闭杀毒软件或防火墙内找到被删除软件点允许

### 17. 各系统卸载cursor及清理残留文件的软件推荐
- **Windows:** Geek (`geek.*********.exe`)
- **Mac:** [点击这里](about:blank)

### 18. Our servers are currently........
我们的服务器目前对非专业用户过载, 您已经使用了免费配额。请几分钟后再试。
![Server Overloaded](https://i.imgur.com/q1W2e3s.png)
**解决方案:**
cursor设置里面退出当前账号, 登录助手点击刷新cursor按钮

### 19、激活失败原因。
- **第一种。**
  - **情况一、** 科学上网了, 关闭之后重启登录助手
  - **情况二、** 目前的网络有限制, 换成热点之后, 重启登录助手
  - 左下角如果出现这个, 就是没有连上服务器。需要换网络。
    ![网络连接失败](https://i.imgur.com/s2C1v7p.png)
- **第二种。**
  看看激活时间是否显示, 显示的话就是已经激活了。不用重复激活。
  ![激活成功](https://i.imgur.com/l7c8t9r.png)

### 20、需要手动修改代码
![手动修改代码提示](https://i.imgur.com/k9u1W3r.png)
**解决方案:** 把你的需求加一句这个: `Please edit the file in small chunks`

### 21、手动配置禁用更新
- **Windows 用户**可以手动禁用自动更新功能:
  1. 关闭所有 Cursor 进程
  2. 删除目录: `C:\Users\<USER>\AppData\Local\cursor-updater`
  3. 创建同名文件: `cursor-updater` (不带扩展名)
  > [一键工具, 运行后, 选择禁用自动更新](about:blank)
- **macOS**
  打开终端, 输入以下命令并执行:
  ```bash
  curl -fsSL https://aizaozao.com/accelerate.php/https://raw.githubusercontent.com/yuaotian/go-cursor-help/refs/heads/master/scripts/run/cursor_mac_id_modifier.sh -o ./cursor_mac_id_modifier.sh && sudo bash ./cursor_mac_id_modifier.sh && rm ./cursor_mac_id_modifier.sh
  ```
- **Linux**
  打开终端, 输入以下命令并执行:
  ```bash
  curl -fsSL https://aizaozao.com/accelerate.php/https://raw.githubusercontent.com/yuaotian/go-cursor-help/refs/heads/master/scripts/run/cursor_linux_id_modifier.sh | sudo bash
  ```
- **Windows**
  打开 PowerShell, 输入以下命令并执行:
  ```powershell
  irm https://raw.githubusercontent.com/yuaotian/go-cursor-help/refs/heads/master/scripts/run/cursor_win_id_modifier.ps1 | iex
  ```

---

## Cursor怎样设置中文
要将Cursor软件设置为中文, 可以按照以下步骤进行操作:
1.  **打开Cursor软件:** 首先, 启动Cursor软件。
2.  **打开命令面板:** 按下键盘组合键`Ctrl + Shift + P`, 这将打开命令面板。
3.  **输入语言配置命令:** 在命令面板的搜索框中输入`Configure Display Language`, 然后按下回车键。
4.  **选择中文:** 在弹出的选项中选择中文 (Chinese) , 系统会提示您重启软件以应用更改。
5.  **重启软件:** 按照提示重启Cursor软件, 完成语言设置。
通过以上步骤, 您就可以顺利将Cursor软件的界面语言更改为中文。
[点击这里可以看图文。](about:blank)

**下面是插件安装 (有些人的下拉框里面没有中文)**
[点击这里就行](about:blank)
**Cursor的安装**
[点击这里就行](about:blank)

---

## Cursor详细使用教程 (看完无敌版本)
- [点击下方链接, 跳转到csdn观看网络大神的cursor教程](about:blank)
- [Cursor零基础小白教程系列 - 创建你的第一个Cursor项目](about:blank)
- [MAC电脑-M芯片-Python环境安装](about:blank)
- [cursor如何设置中文回复](about:blank)
- [Cursor历史记录导出完整指南 - 轻松保存Chat对话记录](https://cursordocs.com/tutorial/02-cursor-chat-history-export)

---

## 可使用模型
- **Claude3.7模型,** 在网络良好并在使用人数较少的地区的情况下可以使用基本在上午或者周六周天

### 一、不可用模型 (所有账号类型)
- **禁用模型:** `ol-preview`, `o1`, `GPT4.5`, `MAX`
- **原因:** 额外单次计费模型, 单次使用收费 **40美分**。

### 二、可用模型列表
#### 1. Chat 模式
| 模型类型 | 具体模型 |
| :--- | :--- |
| 免费/基础计费 | `default`, `claude-3-opus`, `claude-3-haiku`, `claude-3.5-sonnet`, `claude-3.7-sonnet`, `claude-3.7-sonnet-thinking`, `gpt-4`, `o3-mini` |
| 高级模型(可能计费) | `deepseek-v3`, `deepseek-r1`, `grok-2`, `3-opus`, `4o`, `G.PT3.5`, `G.PT4` |
| 合作模型 (需要开启代理的) | `gemini-2全系`, `Claude3.7sonnet`, `Claude-3.7-sonnet-thinking` |

#### 2. 以下模型需要已编辑模式启动新线程即可使用
![编辑模式启动](https://i.imgur.com/r8t0R6j.png)
*选择以下模型, 会有提醒, 然后点击“已编辑模式启动新线程”, 就可以正常使用。*

| 模型类型 | 具体模型 |
| :--- | :--- |
| 免费/基础计费 | `gpt-4o-mini`, `cursor-fast` |

### 三、特殊说明
#### Claude3.7 模型
- **可用条件:** 网络良好 + 使用人数较少的地区 (如欧美非高峰时段)。
- **推荐时段:** 上午或晚上。
- **网络良好的话, 也是可以全天可以用。**
