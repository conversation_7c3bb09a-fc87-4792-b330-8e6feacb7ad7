【Augment Code 登陆助手】新品
────────

智切（Augment Code 登陆助手）里面有augment账号，都是独享试用账号，能一键登录 augment，不需要用户自己注册，方便用户以极低价格使用 augment pro的大部分功能。
· 支持 Claude 4 高级模型 ⭐升级版
· 多IDE支持：VS Code/Cursor
· 每个账号300额度，用完一键换号免重启

套餐定价:
·   2.9元 (日卡/1500额度)
·   7.9元 (周卡/6000额度)
·  12.9元 (半月卡/15000额度)
·  19.9元 (月卡/30000额度) 推荐

购买渠道 (淘宝自动发货):
https://item.taobao.com/item.htm?ft=t&id=953114980777

1. Augment 登陆助手 使用教程: 
https://w1yklj2r7gv.feishu.cn/wiki/JQTHw0eEKiOSI7kvLIqcofMsnbf

2. Augment 官方下载链接
https://www.augmentcode.com/registration?status=already-signed-up

3. Augment 保姆级教程！https://www.bilibili.com/video/BV1LBg9zpEyj/?spm_id_from=333.337.search-card.all.click&vd_source=1c05d5ba65bafd7e024b607706cfacfd

4. 【教程 】在Cursor中安装Augment Code插件
https://linux.do/t/topic/697218

5. 将 Augement 移到右栏，像Cursor一样对话
https://developer.volcengine.com/articles/7497876495386607667

6. Augment Remote Agent （云端功能）入门教程，AI 编程进入多 Agent 时代！ 
https://www.bilibili.com/video/BV1Ze3jzWEP5/?spm_id_from=333.337.search-card.all.click&vd_source=1c05d5ba65bafd7e024b607706cfacfd

────────
【Cursor 登陆助手】
────────

· 可用 Claude 3.7 等模型 (暂不支持 Claude-4)
· 店铺：Cursor直营店

激活码定价 (30天有效):
·   4.9元 (200 额度)
·   8.9元 (500 额度)
·  12.9元 (1000 额度)
·  24.9元 (无限额度/单设备)

购买渠道 (淘宝自动发货):
https://item.taobao.com/item.htm?id=931668414172

相关资源:
· 使用教程: https://w1yklj2r7gv.feishu.cn/wiki/RWz3wokbdihMsXktCyBcx7oCnsg
· Cursor下载: https://cursor.aivora.cn/



如有疑问，请洽群主。