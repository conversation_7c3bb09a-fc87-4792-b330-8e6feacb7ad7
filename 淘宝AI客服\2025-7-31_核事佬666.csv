﻿快捷短语编码,快捷短语,快捷短语分组,快捷短语图片
49,哈喽 宝，在的，有什么需要吗,通用问候,
41,ok,通用问候,
1,哈喽！这是AI编程助手，稳定低成本用AI写代码！Cursor支持3.7，Augment支持Claude 4，超给力！,通用问候,
2,好的亲,通用问候,
3,嗯嗯，好的~ 还有什么我可以帮您的吗？,通用问候,
28,哈喽 宝，在的，有什么想问的吗,通用问候,
70,augment登陆助手 教程：https://w1yklj2r7gv.feishu.cn/wiki/VkM2wKV30iQLTakvxPNcov4mnFc?renamingWikiNode=false,产品介绍,
69,给您一个工具，里面有账号，用这个工具登录就行,产品介绍,
67,购买后发您一个登陆助手。里面有账号，一键登录，无需注册。,产品介绍,
51,cursor登陆助手 使用说明,产品介绍,https://img.alicdn.com/imgextra/i1/2213764935226/O1CN016T2R2e1oTYusGpK93_!!2213764935226-2-ampmedia.png
4,重点说明：咱们是【登录助手激活码】，不是账号密码或充值。激活后助手自动登录独享试用号，体验接近官方Pro功能！,产品介绍,
5,神速发货！付款后激活码和教程【自动秒发】到旺旺或订单，马上激活使用，无需等待！,产品介绍,
6,请放心！登录助手是正规辅助工具，不涉及盗号风险。部分杀软误报属正常，添加信任即可。注重用户安全和隐私！,产品介绍,
25,亲 登陆助手里面有账号，一键登录。不需要自己注册,产品介绍,
30,给您一个工具，里面有账号，用这个工具登录 cursor就行,产品介绍,
7, 【Cursor】200额度4.9元｜500额度8.9元｜1000额度12.9元｜月卡29.9元【Augment】日卡3.9元｜半月卡11.9元｜月卡19.9元推荐！固定价不议价哦~,套餐购买,
8,Cursor套餐30天有效期。Augment按套餐：日卡1天｜周卡7天｜半月卡15天｜月卡30天。从激活当天开始计算！,套餐购买,
9,超低价体验套餐！Cursor几块钱200额度，Augment 2.9元1500额度。直接拍下感受AI助手魅力！,套餐购买,
73,· Cursor登陆助手使用教程: https://w1yklj2r7gv.feishu.cn/wiki/RWz3wokbdihMsXktCyBcx7oCnsg,使用教程,
66,最新可用，昨天刚测试，能用 claude 3.7,使用教程,https://img.alicdn.com/imgextra/i2/2213764935226/O1CN01LeAe1f1oTYuvv3R6X_!!2213764935226-2-ampmedia.png
10, 都支持Win/Mac系统。Cursor助手暂不支持Linux，Augment支持Win/Mac/Linux。IDE支持：Cursor自身编辑器，Augment支持VS Code/Cursor。,使用教程,
11,先装Cursor/VS Code编辑器，再下载助手。Win管理员运行，Mac允许未验证应用。杀软误报请信任！,使用教程,
12,Cursor标准版支持【多设备同时使用】，额度共享。Augment支持多设备激活。换设备直接用同码激活超方便！,产品特点,
13,Cursor:1额度≈1次Claude 3.7问答，Tab补全不计入。Augment:每账号125额度，用完一键切换。推荐500额度套餐日常够用！,产品特点,
14,Cursor暂时不支持 claude 4，支持Claude 3.7、GPT-4、4o等。Augment自动选择Claude 4，模型更强大！具体列表看详情页。,产品特点,
21,都支持Agent智能体功能！除Max等顶级模型外，用Claude 3.7/4跑Agent完全没问题，自动编辑代码效率超高！,产品特点,
56,需要关掉梯子的系统代理，用管理员权限打开工具,常见问题,
15, Cursor:关闭梯子+管理员运行，检查激活码无多余空格。Augment:按教程多点【检查】【获取】按钮。仍有问题联系售后群！,常见问题,
16,提示limit是额度用完。Cursor点【刷新Cursor】换号。Augment点【切换新账号】秒换。账号池充足随便用！,常见问题,
17, Cursor最近确实不稳定，官方加强限制。推荐【Augment】更稳定，无官方限制！https://item.taobao.com/item.htm?ft=t&id=953114980777,常见问题,
18, Cursor需要正确配置代理避免网络问题。Augment【国内直接可用无需梯子】！推荐：https://item.taobao.com/item.htm?ft=t&id=953114980777,常见问题,
31,能使用 agent 自动修改代码,常见问题,https://img.alicdn.com/imgextra/i1/2213764935226/O1CN01F2h5rJ1oTYuqwKHh0_!!2213764935226-2-ampmedia.png
32,"5. 将 Augement 移到右栏，像Cursor一样对话
https://w1yklj2r7gv.feishu.cn/wiki/T48FwESrDiAGPnkQSZzcY4Zzn5c
",常见问题,
33,3. Augment 保姆级教程！https://www.bilibili.com/video/BV1LBg9zpEyj/?spm_id_from=333.337.search-card.all.click&vd_source=1c05d5ba65bafd7e024b607706cfacfd,常见问题,
35,claude 4是付费模型，cursor暂时不支持，能用 claude 3.7等主流模型,常见问题,
50,您发下问题截图,售后服务,
19,遇到任何问题，都可以通过教程里提供的【微信售后群】联系我们，有专业技术支持帮您解决！,售后服务,
20, 先别急！Cursor确实不稳定，推荐换【Augment】能用Claude 4，超稳定！https://item.taobao.com/item.htm?ft=t&id=953114980777 支持退换，多退少补。请问想要哪个套餐？,售后服务,https://img.alicdn.com/imgextra/i4/2213764935226/O1CN01rm3SGp1oTYuzDchl6_!!2213764935226-2-ampmedia.png
26,亲 （1.2 your request）问题，走下这个教程,售后服务,https://img.alicdn.com/imgextra/i2/2213764935226/O1CN01Nv9S221oTYuo784sp_!!2213764935226-2-ampmedia.png
27,亲，（2. too many）问题，走下这个教程,售后服务,https://img.alicdn.com/imgextra/i2/2213764935226/O1CN01m9xyp31oTYuhX6gim_!!2213764935226-2-ampmedia.png
29," 感谢购买！请查看下方9cursor登陆助手0教程开始使用：

https://w1yklj2r7gv.feishu.cn/wiki/RWz3wokbdihMsXktCyBcx7oCnsg",售后服务,
74,"一共有50个号，一个月有效期。
一个号 125额度，1个额度等于一次对话次数",Augment产品,
75,"每个号 125额度，5个号 一共 600额度. 
一额度等于一次对话次数。",Augment产品,
72,"1.1· 号池稳定版 使用教程: 
https://w1yklj2r7gv.feishu.cn/wiki/WiYNwLCnti7kXmkDj5Zcl3hGnMb
",Augment产品,
55,不好意思，augment目前有些问题，可能要明天解决完。,Augment产品,
54,augment遇到些问题，可能要到明天解决，我现在看内置代理能不能解决,Augment产品,
51,可以的亲，放心使用，包售后的,Augment产品,
47,"augment目前问题已经解决。
下载新版 1.0.7插件后。
先退出当前账号。
然后点击
1. 重置
2. 登录，
3. 获取
",Augment产品,
46,"augment 智切插件教程：

https://w1yklj2r7gv.feishu.cn/wiki/JQTHw0eEKiOSI7kvLIqcofMsnbf",Augment产品,
45,智切助手 使用方法,Augment产品,https://img.alicdn.com/imgextra/i2/2213764935226/O1CN01Mcdrzw1oTYumSos0g_!!2213764935226-2-ampmedia.png
42,augment会自动选择 claude 4，不能手动选择,Augment产品,
40,可能是ip，晚点用代理注册一批,Augment产品,
39,augment官方限制了，正在解决这个问题，耐心等待下,Augment产品,
37,卸载重装下 Augment，然后重启 Cursor,Augment产品,
22, 需要Claude 4推荐【Augment】：支持Claude 4，国内直接用，更稳定。日常使用【Cursor】：价格更低，支持Claude 3.7。https://item.taobao.com/item.htm?ft=t&id=953114980777,Augment产品,https://img.alicdn.com/imgextra/i4/2213764935226/O1CN01rm3SGp1oTYuzDchl6_!!2213764935226-2-ampmedia.png
34,点下【检查】和【获取】，多点几次。?【登录】点一次就行，点一次消耗一个账号,Augment产品,
36,亲 这是没额度了。点下【登录】换一个账号，然后点下【检查】和【获取】,Augment产品,https://img.alicdn.com/imgextra/i1/2213764935226/O1CN01GUVxM91oTYul68pAL_!!2213764935226-2-ampmedia.png
23,看中直接拍下，付款后系统自动发【激活码和教程】到旺旺或订单！有问题随时问，包您满意！,购买流程,
24,亲 这个是限制问题，看下这个教程,购买后,https://img.alicdn.com/imgextra/i2/2213764935226/O1CN011NqRNI1oTYumTpj3d_!!2213764935226-2-ampmedia.png
